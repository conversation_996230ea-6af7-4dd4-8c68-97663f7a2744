{"Capacitive": {"founders": ["https://www.linkedin.com/in/rohan-rad<PERSON><PERSON>oon", "https://www.linkedin.com/in/kevintreehan"], "YC_url": "https://www.ycombinator.com/companies/capacitive", "company_linked_page": ["https://www.linkedin.com/company/capacitive"]}, "Anvil": {"founders": ["https://www.linkedin.com/in/daniel-sir<PERSON><PERSON>-cfa-07071255", "https://www.linkedin.com/in/barak-ben-noon"], "YC_url": "https://www.ycombinator.com/companies/anvil", "company_linked_page": ["https://www.linkedin.com/company/anvil-analytics"]}, "Approval AI": {"founders": ["https://www.linkedin.com/in/arjun-la<PERSON><PERSON>i", "https://www.linkedin.com/in/hellyshah"], "YC_url": "https://www.ycombinator.com/companies/approval-ai", "company_linked_page": ["https://www.linkedin.com/company/approval-ai"]}, "Auctor": {"founders": ["https://www.linkedin.com/in/xinan<PERSON>man", "https://www.linkedin.com/in/anthony-sky-ng-thow-hing-9b1352193", "https://www.linkedin.com/in/weihongsun", "https://www.linkedin.com/in/matthewblackburn0"], "YC_url": "https://www.ycombinator.com/companies/auctor", "company_linked_page": ["https://www.linkedin.com/company/getauctor"]}, "neoncoral": {"founders": ["https://www.linkedin.com/in/thomasliao"], "YC_url": "https://www.ycombinator.com/companies/neoncoral", "company_linked_page": []}, "Galen AI": {"founders": ["https://www.linkedin.com/in/virajmehta28", "https://www.linkedin.com/in/priyanka-shrestha-"], "YC_url": "https://www.ycombinator.com/companies/galen-ai", "company_linked_page": ["https://www.linkedin.com/company/galen-ai-inc"]}, "SynthioLabs": {"founders": ["https://www.linkedin.com/in/sahitya-sridhar", "https://www.linkedin.com/in/raja<PERSON><PERSON>-v", "https://www.linkedin.com/in/supreetdeshpande"], "YC_url": "https://www.ycombinator.com/companies/synthiolabs", "company_linked_page": ["https://www.linkedin.com/company/synthiolabs"]}, "Cua": {"founders": ["https://www.linkedin.com/in/francesco-bonacci-70428a121", "https://www.linkedin.com/in/alessandro-puppo"], "YC_url": "https://www.ycombinator.com/companies/cua", "company_linked_page": ["https://www.linkedin.com/company/cua-ai"]}, "Minerva": {"founders": ["https://www.linkedin.com/in/om-agarwal", "https://www.linkedin.com/in/zhudanmeng"], "YC_url": "https://www.ycombinator.com/companies/minerva", "company_linked_page": ["https://www.linkedin.com/company/minervaintelligence"]}, "Kanava AI": {"founders": ["https://www.linkedin.com/in/vikhyath-m-a00357128", "https://www.linkedin.com/in/smitdagli"], "YC_url": "https://www.ycombinator.com/companies/kanava-ai", "company_linked_page": []}, "GroundControl": {"founders": ["https://www.linkedin.com/in/nickcw", "https://www.linkedin.com/in/mehulmshah22", "https://www.linkedin.com/in/matthew-noseworthy-*********"], "YC_url": "https://www.ycombinator.com/companies/groundcontrol", "company_linked_page": ["https://www.linkedin.com/company/groundcontrol-software"]}, "Avallon AI": {"founders": ["https://www.linkedin.com/in/jetsemrick", "https://www.linkedin.com/in/bryan-guin", "https://www.linkedin.com/in/cornelius-schramm-*********"], "YC_url": "https://www.ycombinator.com/companies/avallon-ai", "company_linked_page": ["https://www.linkedin.com/company/avallon"]}, "Bloom": {"founders": ["https://www.linkedin.com/in/sirian-maathuis", "https://www.linkedin.com/in/david-oort-alonso"], "YC_url": "https://www.ycombinator.com/companies/bloom-4", "company_linked_page": ["https://www.linkedin.com/company/bloomlabs-inc"]}, "cubic": {"founders": ["https://www.linkedin.com/in/paulsf", "https://www.linkedin.com/in/allisyao"], "YC_url": "https://www.ycombinator.com/companies/cubic", "company_linked_page": ["https://www.linkedin.com/company/cubic-dev"]}, "Notus Autonomous Systems": {"founders": ["https://www.linkedin.com/in/arhum-jain-54156330b", "https://www.linkedin.com/in/emmanuel-margolin-*********"], "YC_url": "https://www.ycombinator.com/companies/notus-autonomous-systems", "company_linked_page": ["https://www.linkedin.com/company/notus-systems"]}, "Golf": {"founders": ["https://www.linkedin.com/in/antoni-gmitruk-80a268211", "https://www.linkedin.com/in/wojciech-b"], "YC_url": "https://www.ycombinator.com/companies/golf", "company_linked_page": ["https://www.linkedin.com/company/*********"]}, "BitBoard": {"founders": ["https://www.linkedin.com/in/connorandrewjones", "https://www.linkedin.com/in/ambar-choudhury-92681038", "https://www.linkedin.com/in/arcbam"], "YC_url": "https://www.ycombinator.com/companies/bitboard", "company_linked_page": ["https://www.linkedin.com/company/bitboardai"]}, "nao Labs": {"founders": ["https://www.linkedin.com/in/claire-gouze", "https://www.linkedin.com/in/christopheblefari"], "YC_url": "https://www.ycombinator.com/companies/nao-labs", "company_linked_page": ["https://www.linkedin.com/company/getnao"]}, "Vybe": {"founders": ["https://www.linkedin.com/in/fabiendevos", "https://www.linkedin.com/in/quanghoang"], "YC_url": "https://www.ycombinator.com/companies/vybe", "company_linked_page": ["https://www.linkedin.com/company/*********"]}, "Aravolta": {"founders": ["https://www.linkedin.com/in/sutton-jack", "https://www.linkedin.com/in/margarita-groisman"], "YC_url": "https://www.ycombinator.com/companies/aravolta", "company_linked_page": ["https://www.linkedin.com/company/centralaxis"]}, "Kashikoi": {"founders": ["https://www.linkedin.com/in/aaksham", "https://www.linkedin.com/in/timmichaud"], "YC_url": "https://www.ycombinator.com/companies/kashikoi", "company_linked_page": ["https://www.linkedin.com/company/kashikoi"]}, "Plexe": {"founders": ["https://www.linkedin.com/in/vaibhav-dubey-b31930128", "https://www.linkedin.com/in/marc<PERSON>-<PERSON>-be<PERSON><PERSON>"], "YC_url": "https://www.ycombinator.com/companies/plexe", "company_linked_page": ["https://www.linkedin.com/company/plexe-ai"]}, "Frekil": {"founders": ["https://www.linkedin.com/in/shivesh-gupta-iitb", "https://www.linkedin.com/in/nikhiltiwari-iitb"], "YC_url": "https://www.ycombinator.com/companies/frekil", "company_linked_page": ["https://www.linkedin.com/company/frekil"]}, "Chestnut": {"founders": ["https://www.linkedin.com/in/spencercbrown"], "YC_url": "https://www.ycombinator.com/companies/chestnut", "company_linked_page": ["https://www.linkedin.com/company/chestnutmortgage"]}, "QFEX": {"founders": ["https://www.linkedin.com/in/joshua-wharton", "https://www.linkedin.com/in/annanay-kapila"], "YC_url": "https://www.ycombinator.com/companies/qfex", "company_linked_page": ["https://www.linkedin.com/company/entimox", "https://www.linkedin.com/company/qfex"]}, "Blaxel": {"founders": ["https://www.linkedin.com/in/nicolas-lecomte-*********", "https://www.linkedin.com/in/crochetthomas", "https://www.linkedin.com/in/christo<PERSON>-ploujoux", "https://www.linkedin.com/in/charles-drappier-48017962", "https://www.linkedin.com/in/mathis-joffre-30992712a", "https://www.linkedin.com/in/paul-sinai"], "YC_url": "https://www.ycombinator.com/companies/blaxel", "company_linked_page": ["https://www.linkedin.com/company/blaxel-ai"]}, "Atlog": {"founders": ["https://www.linkedin.com/in/john-bettinger", "https://www.linkedin.com/in/v<PERSON><PERSON><PERSON><PERSON>", "https://www.linkedin.com/in/karakkattu"], "YC_url": "https://www.ycombinator.com/companies/atlog", "company_linked_page": ["https://www.linkedin.com/company/atlog-ai"]}, "Waffle": {"founders": ["https://www.linkedin.com/in/diogo-vddc", "https://www.linkedin.com/in/manuj-mishra"], "YC_url": "https://www.ycombinator.com/companies/waffle", "company_linked_page": ["https://www.linkedin.com/company/waffle-ai"]}, "Partcl": {"founders": ["https://www.linkedin.com/in/william-salcedo", "https://www.linkedin.com/in/vamshi<PERSON>ga"], "YC_url": "https://www.ycombinator.com/companies/partcl", "company_linked_page": ["https://www.linkedin.com/company/partcl"]}, "Tropir": {"founders": ["https://www.linkedin.com/in/aarush-kukreja", "https://www.linkedin.com/in/ayush-karupakula"], "YC_url": "https://www.ycombinator.com/companies/tropir", "company_linked_page": ["https://www.linkedin.com/company/tropir"]}, "Willow": {"founders": ["https://www.linkedin.com/in/allan-guo"], "YC_url": "https://www.ycombinator.com/companies/willow", "company_linked_page": ["https://www.linkedin.com/company/willowvoice"]}, "Atum Works": {"founders": ["https://www.linkedin.com/in/malcolm-tisdale", "https://www.linkedin.com/in/matteo-kimura-90879318b", "https://www.linkedin.com/in/luca<PERSON>-p<PERSON><PERSON>-ba3595167"], "YC_url": "https://www.ycombinator.com/companies/atum-works", "company_linked_page": ["https://www.linkedin.com/company/atum-works"]}, "Leeroo": {"founders": ["https://www.linkedin.com/in/ars7", "https://www.linkedin.com/in/alire<PERSON>-moham<PERSON><PERSON>hi", "https://www.linkedin.com/in/majid-yazdani-a9032011"], "YC_url": "https://www.ycombinator.com/companies/leeroo", "company_linked_page": ["https://www.linkedin.com/company/leeroo"]}, "Percival": {"founders": ["https://www.linkedin.com/in/davidzhu10", "https://www.linkedin.com/in/kevin-bi-*********"], "YC_url": "https://www.ycombinator.com/companies/percival", "company_linked_page": ["https://www.linkedin.com/company/percivaltech"]}, "VibeGrade": {"founders": ["https://www.linkedin.com/in/danielmartinezdev", "https://www.linkedin.com/in/musa-aqeel"], "YC_url": "https://www.ycombinator.com/companies/vibegrade", "company_linked_page": ["https://www.linkedin.com/company/gradeassist"]}, "YouLearn": {"founders": ["https://www.linkedin.com/in/achyut-<PERSON><PERSON><PERSON><PERSON>", "https://www.linkedin.com/in/soamikapadia", "https://www.linkedin.com/in/david-yu-*********"], "YC_url": "https://www.ycombinator.com/companies/youlearn", "company_linked_page": ["https://www.linkedin.com/company/youlearnai"]}, "stratify": {"founders": ["https://www.linkedin.com/in/prathamhombal", "https://www.linkedin.com/in/siddhartha-javvaji-23b81617a"], "YC_url": "https://www.ycombinator.com/companies/stratify", "company_linked_page": ["https://www.linkedin.com/company/getstratify"]}, "Sim Studio": {"founders": ["https://www.linkedin.com/in/waleedlatif", "https://www.linkedin.com/in/emir-karabeg-53ab52196"], "YC_url": "https://www.ycombinator.com/companies/simstudio", "company_linked_page": ["https://www.linkedin.com/company/simstudioai"]}, "Clarm": {"founders": ["https://www.linkedin.com/in/alex-usher", "https://www.linkedin.com/in/marcus-storm-mollard-a0514aa1"], "YC_url": "https://www.ycombinator.com/companies/clarm", "company_linked_page": ["https://www.linkedin.com/company/clarm"]}, "Beluga Labs": {"founders": ["https://www.linkedin.com/in/fernando-young-13b713166", "https://www.linkedin.com/in/jack-swisher-77548a165"], "YC_url": "https://www.ycombinator.com/companies/beluga-labs", "company_linked_page": ["https://www.linkedin.com/company/beluga-labs-ai"]}, "Kaelio": {"founders": ["https://www.linkedin.com/in/and<PERSON><PERSON><PERSON><PERSON>v", "https://www.linkedin.com/in/luca-martial"], "YC_url": "https://www.ycombinator.com/companies/kaelio", "company_linked_page": ["https://www.linkedin.com/company/kaelio-ai"]}, "Zenobia Pay": {"founders": ["https://www.linkedin.com/in/theodore-li-0425b5177", "https://www.linkedin.com/in/rprendergast"], "YC_url": "https://www.ycombinator.com/companies/zenobia-pay", "company_linked_page": ["https://www.linkedin.com/company/zenobia-pay"]}, "Zeon Systems": {"founders": ["https://www.linkedin.com/in/bronte-kolar", "https://www.linkedin.com/in/tahirdme"], "YC_url": "https://www.ycombinator.com/companies/zeon-systems", "company_linked_page": ["https://www.linkedin.com/company/zeon-systems"]}, "The Robot Learning Company": {"founders": ["https://www.linkedin.com/in/jannik-grothusen"], "YC_url": "https://www.ycombinator.com/companies/the-robot-learning-company", "company_linked_page": ["https://www.linkedin.com/company/the-robot-learning-company"]}, "HelixDB": {"founders": ["https://www.linkedin.com/in/george-curtis-12b232a5", "https://www.linkedin.com/in/xaviercochran"], "YC_url": "https://www.ycombinator.com/companies/helixdb", "company_linked_page": ["https://www.linkedin.com/company/helixdb"]}, "Chonkie": {"founders": ["https://www.linkedin.com/in/bhavnicksm", "https://www.linkedin.com/in/shreyashnigam"], "YC_url": "https://www.ycombinator.com/companies/chonkie", "company_linked_page": ["https://www.linkedin.com/company/chonkieai"]}, "Almond": {"founders": ["https://www.linkedin.com/in/shawnpatel", "https://www.linkedin.com/in/saba-khalilnaji-*********", "https://www.linkedin.com/in/saba-khalilnaji"], "YC_url": "https://www.ycombinator.com/companies/almond-2", "company_linked_page": ["https://www.linkedin.com/company/almond-inc"]}, "Claim Health": {"founders": ["https://www.linkedin.com/in/jayenram", "https://www.linkedin.com/in/kevincalcado"], "YC_url": "https://www.ycombinator.com/companies/claim-health", "company_linked_page": ["https://www.linkedin.com/company/claim-health"]}, "VoiceOS": {"founders": ["https://www.linkedin.com/in/jonah-daian", "https://www.linkedin.com/in/francis-brokering", "https://www.linkedin.com/in/ari-ramsan", "https://www.linkedin.com/in/parker-bjur-7bb368178"], "YC_url": "https://www.ycombinator.com/companies/voiceos", "company_linked_page": ["https://www.linkedin.com/company/wako-ai"]}, "Chiron": {"founders": ["https://www.linkedin.com/in/max-booth-*********", "https://www.linkedin.com/in/james-booth-project-chiron"], "YC_url": "https://www.ycombinator.com/companies/chiron", "company_linked_page": []}, "Jeevy Fabrication": {"founders": ["https://www.linkedin.com/in/jeevesh-k-4aa73a139", "https://www.linkedin.com/in/vinay-k-b09034186"], "YC_url": "https://www.ycombinator.com/companies/jeevy-fabrication", "company_linked_page": ["https://www.linkedin.com/company/jeevy-fabrication"]}, "Trapeze": {"founders": ["https://www.linkedin.com/in/betchang", "https://www.linkedin.com/in/chrismychen"], "YC_url": "https://www.ycombinator.com/companies/trapeze", "company_linked_page": ["https://www.linkedin.com/company/trapezehealth"]}, "Tinfoil": {"founders": ["https://www.linkedin.com/in/sacha-servan-schreiber-*********", "https://www.linkedin.com/in/nate-sales", "https://www.linkedin.com/in/jdrean", "https://www.linkedin.com/in/tanya-verma-130a23124"], "YC_url": "https://www.ycombinator.com/companies/tinfoil", "company_linked_page": ["https://www.linkedin.com/company/tinfoil", "https://www.linkedin.com/company/Tinfoil"]}, "The LLM Data Company": {"founders": ["https://www.linkedin.com/in/daanish<PERSON>zi", "https://www.linkedin.com/in/thegavinbains", "https://www.linkedin.com/in/jpbesgen"], "YC_url": "https://www.ycombinator.com/companies/the-llm-data-company", "company_linked_page": ["https://www.linkedin.com/company/the-llm-data-company"]}, "Operative": {"founders": ["https://www.linkedin.com/in/christopher-settles", "https://www.linkedin.com/in/erik<PERSON><PERSON>lla"], "YC_url": "https://www.ycombinator.com/companies/operative", "company_linked_page": ["https://www.linkedin.com/company/operative-sh"]}, "Parsewise": {"founders": ["https://www.linkedin.com/in/gergely-csegzi", "https://www.linkedin.com/in/mwhofer"], "YC_url": "https://www.ycombinator.com/companies/parsewise", "company_linked_page": ["https://www.linkedin.com/company/parsewise"]}, "Better Auth": {"founders": ["https://www.linkedin.com/in/bekacru"], "YC_url": "https://www.ycombinator.com/companies/better-auth", "company_linked_page": ["https://www.linkedin.com/company/better-auth"]}, "Bluejay": {"founders": ["https://www.linkedin.com/in/farazmsiddiqi", "https://www.linkedin.com/in/rohanvas"], "YC_url": "https://www.ycombinator.com/companies/bluejay", "company_linked_page": ["https://www.linkedin.com/company/get-bluejay"]}, "Kestral": {"founders": ["https://www.linkedin.com/in/xiebernard", "https://www.linkedin.com/in/brian-y-kim-16b7aa63"], "YC_url": "https://www.ycombinator.com/companies/kestral", "company_linked_page": ["https://www.linkedin.com/company/kestral-team"]}, "Attune": {"founders": ["https://www.linkedin.com/in/xinding3", "https://www.linkedin.com/in/lehaozhang"], "YC_url": "https://www.ycombinator.com/companies/attune", "company_linked_page": ["https://www.linkedin.com/company/attunehq"]}, "PgDog": {"founders": ["https://www.linkedin.com/in/levkk"], "YC_url": "https://www.ycombinator.com/companies/pgdog", "company_linked_page": ["https://www.linkedin.com/company/pgdog"]}, "Caucus": {"founders": ["https://www.linkedin.com/in/tyler-chen-43570b241", "https://www.linkedin.com/in/theami<PERSON><PERSON><PERSON>", "https://www.linkedin.com/in/amir-farahani-21745b216"], "YC_url": "https://www.ycombinator.com/companies/caucus", "company_linked_page": ["https://www.linkedin.com/company/caucusgov"]}, "Kirana AI": {"founders": ["https://www.linkedin.com/in/nicholas-sleeper-*********", "https://www.linkedin.com/in/prateek-pinisetti-*********"], "YC_url": "https://www.ycombinator.com/companies/kirana-ai", "company_linked_page": []}, "Mbodi AI": {"founders": ["https://www.linkedin.com/in/xavierchi", "https://www.linkedin.com/in/sebastian-peralta-87493a135", "https://www.linkedin.com/in/sebbyjp"], "YC_url": "https://www.ycombinator.com/companies/mbodi-ai", "company_linked_page": ["https://www.linkedin.com/company/mbodiai"]}, "Relixir": {"founders": ["https://www.linkedin.com/in/seandorje", "https://www.linkedin.com/in/dennis-zax-*********"], "YC_url": "https://www.ycombinator.com/companies/relixir", "company_linked_page": ["https://www.linkedin.com/company/relixir"]}, "Janus": {"founders": ["https://www.linkedin.com/in/shivump1", "https://www.linkedin.com/in/jet-wu-cmu"], "YC_url": "https://www.ycombinator.com/companies/janus", "company_linked_page": ["https://www.linkedin.com/company/withjanus"]}, "Hemut": {"founders": ["https://www.linkedin.com/in/loki-cheema", "https://www.linkedin.com/in/anthonynguerrera"], "YC_url": "https://www.ycombinator.com/companies/hemut", "company_linked_page": ["https://www.linkedin.com/company/hemut"]}, "Zero": {"founders": ["https://www.linkedin.com/in/ajxb", "https://www.linkedin.com/in/nizar-abi-zaher-*********"], "YC_url": "https://www.ycombinator.com/companies/zero", "company_linked_page": ["https://www.linkedin.com/company/zerodotemail"]}, "ValueMate": {"founders": ["https://www.linkedin.com/in/pietro-demicheli", "https://www.linkedin.com/in/anir-prativadi-4aa4881ba"], "YC_url": "https://www.ycombinator.com/companies/valuemate", "company_linked_page": ["https://www.linkedin.com/company/valuemate-ai"]}, "Bramante Biologics": {"founders": ["https://www.linkedin.com/in/scottstankey", "https://www.linkedin.com/in/micha<PERSON>-ma<PERSON>ci"], "YC_url": "https://www.ycombinator.com/companies/bramante-biologics", "company_linked_page": ["https://www.linkedin.com/company/bramante-biologics"]}, "Scalar Field": {"founders": ["https://www.linkedin.com/in/amand<PERSON>-singh0", "https://www.linkedin.com/in/ramakant-yadav1"], "YC_url": "https://www.ycombinator.com/companies/scalar-field", "company_linked_page": ["https://www.linkedin.com/company/scalarfield"]}, "Eloquent AI": {"founders": ["https://www.linkedin.com/in/aldo-lipani", "https://www.linkedin.com/in/tugcebulut"], "YC_url": "https://www.ycombinator.com/companies/eloquent-ai", "company_linked_page": ["https://www.linkedin.com/company/eloquentai"]}, "MorphoAI": {"founders": ["https://www.linkedin.com/in/andrew-spielberg-82073926a", "https://www.linkedin.com/in/aynaarora"], "YC_url": "https://www.ycombinator.com/companies/morphoai", "company_linked_page": ["https://www.linkedin.com/company/morphoai"]}, "Vassar Robotics": {"founders": ["https://www.linkedin.com/in/charles-zheng<PERSON>-yong"], "YC_url": "https://www.ycombinator.com/companies/vassar-robotics", "company_linked_page": []}, "Boost Robotics": {"founders": ["https://www.linkedin.com/in/hardiksingh", "https://www.linkedin.com/in/kumar<PERSON>s"], "YC_url": "https://www.ycombinator.com/companies/boost-robotics", "company_linked_page": ["https://www.linkedin.com/company/boost-robotics"]}, "Sygaldry Technologies": {"founders": ["https://www.linkedin.com/in/chad-rigetti-9891562", "https://www.linkedin.com/in/idaliafriedson"], "YC_url": "https://www.ycombinator.com/companies/sygaldry-technologies", "company_linked_page": ["https://www.linkedin.com/company/sygaldry-technologies"]}, "ParaQuery": {"founders": ["https://www.linkedin.com/in/winwang"], "YC_url": "https://www.ycombinator.com/companies/paraquery", "company_linked_page": ["https://www.linkedin.com/company/paraquery"]}, "Klavis AI": {"founders": ["https://www.linkedin.com/in/xiang<PERSON>-zeng", "https://www.linkedin.com/in/zihaolin123", "https://www.linkedin.com/in/zihao-lin-14137716b"], "YC_url": "https://www.ycombinator.com/companies/klavis-ai", "company_linked_page": ["https://www.linkedin.com/company/klavis-ai"]}, "Third Chair": {"founders": ["https://www.linkedin.com/in/shouryalala", "https://www.linkedin.com/in/yoav-zimmerman-05653252"], "YC_url": "https://www.ycombinator.com/companies/third-chair", "company_linked_page": ["https://www.linkedin.com/company/watchdog-sf"]}, "Vesence": {"founders": ["https://www.linkedin.com/in/ludvig-swanstr%C3%B6m-73ab49114", "https://www.linkedin.com/in/hen<PERSON>-hansson-1571a8187"], "YC_url": "https://www.ycombinator.com/companies/vesence", "company_linked_page": ["https://www.linkedin.com/company/vesence"]}, "PowerMatrix": {"founders": ["https://www.linkedin.com/in/xufu-ren-58477924b", "https://www.linkedin.com/in/boronghu"], "YC_url": "https://www.ycombinator.com/companies/powermatrix", "company_linked_page": ["https://www.linkedin.com/company/pwrmx"]}, "Jazzberry": {"founders": ["https://www.linkedin.com/in/marco-dewey", "https://www.linkedin.com/in/mateo-perez"], "YC_url": "https://www.ycombinator.com/companies/jazzberry", "company_linked_page": ["https://www.linkedin.com/company/jazzberry-ai"]}, "Besimple AI": {"founders": ["https://www.linkedin.com/in/8illwang", "https://www.linkedin.com/in/yzhong94"], "YC_url": "https://www.ycombinator.com/companies/besimple-ai", "company_linked_page": ["https://www.linkedin.com/company/besimple-ai"]}, "Alkali": {"founders": ["https://www.linkedin.com/in/emmett-goodman", "https://www.linkedin.com/in/victor-v-a514248b"], "YC_url": "https://www.ycombinator.com/companies/alkali", "company_linked_page": ["https://www.linkedin.com/company/alkali-engineering"]}, "Human Behavior": {"founders": ["https://www.linkedin.com/in/ckawediya", "https://www.linkedin.com/in/skyler-ji", "https://www.linkedin.com/in/amoghchaturvedi1"], "YC_url": "https://www.ycombinator.com/companies/human-behavior", "company_linked_page": []}, "QualGent": {"founders": ["https://www.linkedin.com/in/shivam-agrawal", "https://www.linkedin.com/in/aaron-y-a12518ab"], "YC_url": "https://www.ycombinator.com/companies/qualgent", "company_linked_page": ["https://www.linkedin.com/company/qualgent"]}, "Prism AI": {"founders": ["https://www.linkedin.com/in/alexyliuswe", "https://www.linkedin.com/in/landtantichot", "https://www.linkedin.com/in/rkhanna23"], "YC_url": "https://www.ycombinator.com/companies/prism-ai", "company_linked_page": ["https://www.linkedin.com/company/prismreplay"]}, "Novoflow": {"founders": ["https://www.linkedin.com/in/mathieurihet", "https://www.linkedin.com/in/thegeorgesc"], "YC_url": "https://www.ycombinator.com/companies/novoflow", "company_linked_page": ["https://www.linkedin.com/company/novoflow"]}, "Theta Software": {"founders": ["https://www.linkedin.com/in/rayan-garg", "https://www.linkedin.com/in/tanmayxsharma", "https://www.linkedin.com/in/gurvir-s"], "YC_url": "https://www.ycombinator.com/companies/theta-software", "company_linked_page": ["https://www.linkedin.com/company/try-theta-software"]}, "Lyra": {"founders": ["https://www.linkedin.com/in/courtnemarland", "https://www.linkedin.com/in/taeksoo-kwon"], "YC_url": "https://www.ycombinator.com/companies/lyrahq", "company_linked_page": ["https://www.linkedin.com/company/lyra-so"]}, "Cleon": {"founders": ["https://www.linkedin.com/in/rohan-gupta1", "https://www.linkedin.com/in/alexand<PERSON>-zisi<PERSON><PERSON>-a95592149", "https://www.linkedin.com/in/ricardo-pantaleon-9b6959135"], "YC_url": "https://www.ycombinator.com/companies/cleon", "company_linked_page": ["https://www.linkedin.com/company/cleon-yc-x25"]}, "throxy": {"founders": ["https://www.linkedin.com/in/bmerey", "https://www.linkedin.com/in/arnau-ayerbe", "https://www.linkedin.com/in/pjdepargar"], "YC_url": "https://www.ycombinator.com/companies/throxy", "company_linked_page": ["https://www.linkedin.com/company/throxy"]}, "Lucis": {"founders": ["https://www.linkedin.com/in/bdebever", "https://www.linkedin.com/in/maximeberthelot", "https://www.linkedin.com/in/maxgue<PERSON>"], "YC_url": "https://www.ycombinator.com/companies/lucis", "company_linked_page": ["https://www.linkedin.com/company/zero-health-eu"]}, "Flott HQ": {"founders": ["https://www.linkedin.com/in/kanvaly-fadiga", "https://www.linkedin.com/in/sylvanus-mahe"], "YC_url": "https://www.ycombinator.com/companies/flott-hq", "company_linked_page": ["https://www.linkedin.com/company/flott-hq"]}, "Crimson": {"founders": ["https://www.linkedin.com/in/amine-amor-4a7a42135", "https://www.linkedin.com/in/david<PERSON>back", "https://www.linkedin.com/in/mark-feldner"], "YC_url": "https://www.ycombinator.com/companies/crimson", "company_linked_page": ["https://www.linkedin.com/company/*********"]}, "Casco": {"founders": ["https://www.linkedin.com/in/iansaultz", "https://www.linkedin.com/in/renebrandel"], "YC_url": "https://www.ycombinator.com/companies/casco", "company_linked_page": ["https://www.linkedin.com/company/getcasco"]}, "Nomi": {"founders": ["https://www.linkedin.com/in/nullswan"], "YC_url": "https://www.ycombinator.com/companies/nomi", "company_linked_page": ["https://www.linkedin.com/company/nomihq"]}, "Morphik": {"founders": ["https://www.linkedin.com/in/arnavagrawal03", "https://www.linkedin.com/in/adityav369"], "YC_url": "https://www.ycombinator.com/companies/morphik", "company_linked_page": ["https://www.linkedin.com/company/morphik"]}, "RunRL": {"founders": ["https://www.linkedin.com/in/agritsevskiy"], "YC_url": "https://www.ycombinator.com/companies/runrl", "company_linked_page": ["https://www.linkedin.com/company/runrl"]}, "Ticket Wallet": {"founders": ["https://www.linkedin.com/in/ryan-clare-*********", "https://www.linkedin.com/in/truman-gelnovatch-52442218a"], "YC_url": "https://www.ycombinator.com/companies/ticket-wallet", "company_linked_page": ["https://www.linkedin.com/company/ticket-wallet"]}, "Kaizen": {"founders": ["https://www.linkedin.com/in/ken-acquah-45b397123", "https://www.linkedin.com/in/kenneth-acquah-45b397123", "https://www.linkedin.com/in/michael-silver-*********"], "YC_url": "https://www.ycombinator.com/companies/kaizen", "company_linked_page": ["https://www.linkedin.com/company/kaizen-automation-inc"]}, "HABIT": {"founders": ["https://www.linkedin.com/in/harishkpalani", "https://www.linkedin.com/in/micha<PERSON><PERSON><PERSON>"], "YC_url": "https://www.ycombinator.com/companies/habit", "company_linked_page": ["https://www.linkedin.com/company/habitrobotics"]}, "Clidey": {"founders": ["https://www.linkedin.com/in/ahristozov", "https://www.linkedin.com/in/hemangkandwal"], "YC_url": "https://www.ycombinator.com/companies/clidey", "company_linked_page": ["https://www.linkedin.com/company/clidey"]}, "Opusense AI": {"founders": ["https://www.linkedin.com/in/royacody", "https://www.linkedin.com/in/micha<PERSON>-bacani"], "YC_url": "https://www.ycombinator.com/companies/opusense-ai", "company_linked_page": ["https://www.linkedin.com/company/opusense"]}, "Labric": {"founders": ["https://www.linkedin.com/in/connor-hogan-*********", "https://www.linkedin.com/in/caitlin-hogan-*********"], "YC_url": "https://www.ycombinator.com/companies/labric", "company_linked_page": ["https://www.linkedin.com/company/labric-inc"]}, "Photonium": {"founders": ["https://www.linkedin.com/in/adam-mhatre-5b8aaa230", "https://www.linkedin.com/in/jennifer-y-song"], "YC_url": "https://www.ycombinator.com/companies/photonium", "company_linked_page": ["https://www.linkedin.com/company/photonium-optics"]}, "Sixtyfour": {"founders": ["https://www.linkedin.com/in/christopher-price-59a24b178", "https://www.linkedin.com/in/saarthshah"], "YC_url": "https://www.ycombinator.com/companies/sixtyfour", "company_linked_page": ["https://www.linkedin.com/company/sixtyfourai"]}, "Odapt": {"founders": ["https://www.linkedin.com/in/madhav-m", "https://www.linkedin.com/in/ende-shen", "https://www.linkedin.com/in/lucas-negritto-b942b8163"], "YC_url": "https://www.ycombinator.com/companies/odapt", "company_linked_page": ["https://www.linkedin.com/company/odaptai"]}, "Clado": {"founders": ["https://www.linkedin.com/in/eric-mao", "https://www.linkedin.com/in/toomzheng"], "YC_url": "https://www.ycombinator.com/companies/clado", "company_linked_page": ["https://www.linkedin.com/company/clado-ai"]}, "Combinely": {"founders": ["https://www.linkedin.com/in/arthur-granacher", "https://www.linkedin.com/in/tominvernizzi"], "YC_url": "https://www.ycombinator.com/companies/combinely", "company_linked_page": ["https://www.linkedin.com/company/combinely"]}, "Docket": {"founders": ["https://www.linkedin.com/in/boris-skurikhin", "https://www.linkedin.com/in/nishant-h"], "YC_url": "https://www.ycombinator.com/companies/docket", "company_linked_page": ["https://www.linkedin.com/company/docketqa"]}, "SAVA": {"founders": ["https://www.linkedin.com/in/alessiotoniolo", "https://www.linkedin.com/in/vedic-patel-*********", "https://www.linkedin.com/in/jakob-knudsen5"], "YC_url": "https://www.ycombinator.com/companies/sava", "company_linked_page": ["https://www.linkedin.com/company/savarobotics"]}, "Godela": {"founders": ["https://www.linkedin.com/in/cinnamonsipper", "https://www.linkedin.com/in/abhi<PERSON>-pranav-pamarty-6b2453152"], "YC_url": "https://www.ycombinator.com/companies/godela", "company_linked_page": ["https://www.linkedin.com/company/godela-ai"]}, "BitPatrol": {"founders": ["https://www.linkedin.com/in/~chris"], "YC_url": "https://www.ycombinator.com/companies/bitpatrol", "company_linked_page": ["https://www.linkedin.com/company/bitpatrol"]}, "Aegis": {"founders": ["https://www.linkedin.com/in/dhanya-shah-97878835b", "https://www.linkedin.com/in/krishang-todi-a6525b135", "https://www.linkedin.com/in/aarav-bajaj-408ab01b1"], "YC_url": "https://www.ycombinator.com/companies/aegis", "company_linked_page": ["https://www.linkedin.com/company/aegis-yc-x25"]}, "Cascade Space": {"founders": ["https://www.linkedin.com/in/arlena<PERSON>ham", "https://www.linkedin.com/in/jacob-portukalian"], "YC_url": "https://www.ycombinator.com/companies/cascade-space", "company_linked_page": ["https://www.linkedin.com/company/cascade-space"]}, "Bond": {"founders": ["https://www.linkedin.com/in/chloesamaha", "https://www.linkedin.com/in/renesultan"], "YC_url": "https://www.ycombinator.com/companies/bond", "company_linked_page": ["https://www.linkedin.com/company/thebondapp"]}, "Blueshoe": {"founders": ["https://www.linkedin.com/in/kywan", "https://www.linkedin.com/in/casey-og"], "YC_url": "https://www.ycombinator.com/companies/blueshoe", "company_linked_page": ["https://www.linkedin.com/company/blueshoeai"]}, "sieve": {"founders": ["https://www.linkedin.com/in/savannah-tynan-13272a157", "https://www.linkedin.com/in/lu-nicole"], "YC_url": "https://www.ycombinator.com/companies/usesieve", "company_linked_page": ["https://www.linkedin.com/company/usesieve"]}, "Airweave": {"founders": ["https://www.linkedin.com/in/lennert<PERSON>sen", "https://www.linkedin.com/in/orhan-rauf-a<PERSON><PERSON><PERSON>"], "YC_url": "https://www.ycombinator.com/companies/airweave", "company_linked_page": []}, "Theorem": {"founders": ["https://www.linkedin.com/in/rajashreeagrawal", "https://www.linkedin.com/in/jason-gross-86b72125"], "YC_url": "https://www.ycombinator.com/companies/theorem-2", "company_linked_page": ["https://www.linkedin.com/company/theoremlabs"]}, "text.ai": {"founders": ["https://www.linkedin.com/in/parasmaniar", "https://www.linkedin.com/in/rushishah01", "https://www.linkedin.com/in/prahar-patel"], "YC_url": "https://www.ycombinator.com/companies/text-ai", "company_linked_page": ["https://www.linkedin.com/company/textdotai"]}, "StarSling": {"founders": ["https://www.linkedin.com/in/worku", "https://www.linkedin.com/in/yonas-beshawred"], "YC_url": "https://www.ycombinator.com/companies/starsling", "company_linked_page": ["https://www.linkedin.com/company/starslingdev"]}, "Wavedash": {"founders": ["https://www.linkedin.com/in/kylerblue", "https://www.linkedin.com/in/franzwarning", "https://www.linkedin.com/in/in-in-in"], "YC_url": "https://www.ycombinator.com/companies/wavedash", "company_linked_page": ["https://www.linkedin.com/company/wavedashgg"]}, "Cactus": {"founders": ["https://www.linkedin.com/in/ajith-govind", "https://www.linkedin.com/in/joshi-avinash"], "YC_url": "https://www.ycombinator.com/companies/oncactus", "company_linked_page": ["https://www.linkedin.com/company/oncactus"]}, "LineWise": {"founders": ["https://www.linkedin.com/in/zhichu-ren", "https://www.linkedin.com/in/tanachartkujareevanich", "https://www.linkedin.com/in/wenbo-zhang6"], "YC_url": "https://www.ycombinator.com/companies/linewise", "company_linked_page": ["https://www.linkedin.com/company/linewise"]}, "Code Four": {"founders": ["https://www.linkedin.com/in/natnaelkahssay", "https://www.linkedin.com/in/dylan-nguyenn", "https://www.linkedin.com/in/cheng-george"], "YC_url": "https://www.ycombinator.com/companies/code-four", "company_linked_page": ["https://www.linkedin.com/company/codefourus"]}, "Lumari": {"founders": ["https://www.linkedin.com/in/samarthlamba", "https://www.linkedin.com/in/eshanimehta"], "YC_url": "https://www.ycombinator.com/companies/lumari", "company_linked_page": ["https://www.linkedin.com/company/lumari-io"]}, "mlop": {"founders": ["https://www.linkedin.com/in/jqssun", "https://www.linkedin.com/in/lakeesiv"], "YC_url": "https://www.ycombinator.com/companies/mlop", "company_linked_page": ["https://www.linkedin.com/company/mlop"]}, "Den": {"founders": ["https://www.linkedin.com/in/linustalacko", "https://www.linkedin.com/in/justin1ee"], "YC_url": "https://www.ycombinator.com/companies/den", "company_linked_page": ["https://www.linkedin.com/company/deninc"]}, "Propolis": {"founders": ["https://www.linkedin.com/in/matt-white-4750397b", "https://www.linkedin.com/in/marc-papazian", "https://www.linkedin.com/in/mattmattmattmatt"], "YC_url": "https://www.ycombinator.com/companies/propolis", "company_linked_page": ["https://www.linkedin.com/company/propolis-ai"]}, "Cotool": {"founders": ["https://www.linkedin.com/in/mmpollard", "https://www.linkedin.com/in/eddieconk", "https://www.linkedin.com/in/logan-carmody"], "YC_url": "https://www.ycombinator.com/companies/cotool", "company_linked_page": ["https://www.linkedin.com/company/cotool-ai"]}, "Delty": {"founders": ["https://www.linkedin.com/in/cat-catherine-z<PERSON>", "https://www.linkedin.com/in/lalitkundu"], "YC_url": "https://www.ycombinator.com/companies/delty", "company_linked_page": ["https://www.linkedin.com/company/delty-ai"]}, "Nimbic AI": {"founders": ["https://www.linkedin.com/in/ishanmehta2", "https://www.linkedin.com/in/iskhare"], "YC_url": "https://www.ycombinator.com/companies/nimbic-ai", "company_linked_page": ["https://www.linkedin.com/company/nimbic-ai"]}, "Moby Analytics": {"founders": ["https://www.linkedin.com/in/thomas-rapilly-00481167", "https://www.linkedin.com/in/cl%C3%A9ment-sengelen-09261354", "https://www.linkedin.com/in/dimitri-kassubeck-69764647"], "YC_url": "https://www.ycombinator.com/companies/moby-analytics", "company_linked_page": []}, "Rimba": {"founders": ["https://www.linkedin.com/in/akshay326", "https://www.linkedin.com/in/timothydaniel09"], "YC_url": "https://www.ycombinator.com/companies/rimba", "company_linked_page": ["https://www.linkedin.com/company/rimba-compliance-automation"]}, "WorkDone": {"founders": ["https://www.linkedin.com/in/odinalex", "https://www.linkedin.com/in/joel-n-h-stern-07a4342", "https://www.linkedin.com/in/d<PERSON><PERSON><PERSON><PERSON>v", "https://www.linkedin.com/in/yudovskiy"], "YC_url": "https://www.ycombinator.com/companies/workdone", "company_linked_page": ["https://www.linkedin.com/company/wrkdn"]}, "Probo": {"founders": ["https://www.linkedin.com/in/gearnode", "https://www.linkedin.com/in/antoinebouchardy"], "YC_url": "https://www.ycombinator.com/companies/probo", "company_linked_page": ["https://www.linkedin.com/company/getprobo"]}, "MindFort": {"founders": ["https://www.linkedin.com/in/akulguptax", "https://www.linkedin.com/in/samberston", "https://www.linkedin.com/in/brandon-veiseh-77762113a"], "YC_url": "https://www.ycombinator.com/companies/mindfort", "company_linked_page": ["https://www.linkedin.com/company/*********"]}, "chrt": {"founders": ["https://www.linkedin.com/in/kyle-reagan-surfs", "https://www.linkedin.com/in/aaroncarver"], "YC_url": "https://www.ycombinator.com/companies/chrt", "company_linked_page": ["https://www.linkedin.com/company/chrt-inc"]}, "Mesmer": {"founders": ["https://www.linkedin.com/in/lucasbrunosilva", "https://www.linkedin.com/in/sergio-bergmann", "https://www.linkedin.com/in/joaopdepaula"], "YC_url": "https://www.ycombinator.com/companies/mesmer", "company_linked_page": ["https://www.linkedin.com/company/mesmer-co"]}}