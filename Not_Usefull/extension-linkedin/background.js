// background.js

// Listener for messages from the content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "generateMessage") {
        // Get the saved system prompt from storage
        chrome.storage.sync.get(['systemPrompt'], async (result) => {
            const systemPrompt = result.systemPrompt || "You are a helpful assistant. Based on the provided company overview, write a concise and professional outreach message. Start with a personalized hook that references their mission or values. Keep the message under 100 words.";
            
            const companyOverview = request.overview;

            // Construct the prompt for the Gemini API
            const fullPrompt = `${systemPrompt}\n\nHere is the company's 'About' section:\n\n---\n${companyOverview}\n---\n\nNow, please generate the personalized message.`;

            // Call the Gemini API
            try {
                const generatedText = await callGeminiAPI(fullPrompt);
                // Send the generated text back to the content script
                sendResponse({ status: 'success', message: generatedText });
            } catch (error) {
                console.error('Error calling Gemini API:', error);
                sendResponse({ status: 'error', message: 'Failed to generate message. See background console for details.' });
            }
        });

        // Return true to indicate that we will send a response asynchronously
        return true;
    }
});

/**
 * Calls the Gemini API to generate content.
 * @param {string} prompt The complete prompt to send to the model.
 * @returns {Promise<string>} The generated text from the API.
 */
async function callGeminiAPI(prompt) {
    // IMPORTANT: In a real extension, you should not expose an API key like this.
    // This example uses the built-in authentication provided by the environment.
    const apiKey = ""; // Leave empty to use the environment's provided key.
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

    const payload = {
        contents: [{
            role: "user",
            parts: [{ text: prompt }]
        }]
    };

    const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
    });

    if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API request failed with status ${response.status}: ${errorBody}`);
    }

    const result = await response.json();

    if (result.candidates && result.candidates.length > 0 &&
        result.candidates[0].content && result.candidates[0].content.parts &&
        result.candidates[0].content.parts.length > 0) {
        return result.candidates[0].content.parts[0].text;
    } else {
        // Handle cases where the response structure is unexpected or content is missing
        console.warn("Unexpected API response structure:", result);
        return "Could not generate a message due to an unexpected API response.";
    }
}
