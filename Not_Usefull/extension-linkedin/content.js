// content.js

let generatedMessage = null;
let isLoading = false;
let isError = false;

/**
 * Finds the "About" section on a LinkedIn company page and extracts its text.
 * LinkedIn's class names can be unstable. This function tries a few selectors.
 * @returns {string|null} The text of the about section, or null if not found.
 */
function getCompanyOverview() {
    // Selector for the "About" section heading
    const aboutHeading = Array.from(document.querySelectorAll('h2')).find(
        h2 => h2.textContent.trim() === 'Overwiew'
    );

    if (!aboutHeading) {
        console.log('AI Helper: "About" heading not found.');
        return null;
    }

    // The content is usually in a <p> tag within the same parent card element.
    const parentCard = aboutHeading.closest('section');
    if (!parentCard) {
        console.log('AI Helper: Parent section of "About" heading not found.');
        return null;
    }

    const overviewParagraph = parentCard.querySelector('p');
    if (overviewParagraph && overviewParagraph.textContent.trim().length > 20) {
        return overviewParagraph.textContent.trim();
    }

    console.log('AI Helper: Could not find the overview paragraph with sufficient content.');
    return null;
}

/**
 * Sends the scraped overview text to the background script to generate a message.
 */
function triggerMessageGeneration() {
    const overviewText = getCompanyOverview();

    if (overviewText && !isLoading) {
        console.log('AI Helper: Found overview, sending to background script.');
        isLoading = true;
        isError = false;
        generatedMessage = null; // Reset previous message

        chrome.runtime.sendMessage(
            { action: "generateMessage", overview: overviewText },
            (response) => {
                isLoading = false;
                if (chrome.runtime.lastError) {
                    console.error('AI Helper Error:', chrome.runtime.lastError.message);
                    isError = true;
                    generatedMessage = "Error: " + chrome.runtime.lastError.message;
                    return;
                }

                if (response.status === 'success') {
                    console.log('AI Helper: Message received from background.');
                    generatedMessage = response.message;
                } else {
                    console.error('AI Helper Error:', response.message);
                    isError = true;
                    generatedMessage = "Error: " + response.message;
                }
            }
        );
    } else if (!overviewText) {
        console.log('AI Helper: No company overview found on this page.');
    }
}

/**
 * Injects the generated message into the LinkedIn message compose box.
 * @param {HTMLElement} composeBox The message compose text area element.
 */
function populateMessageBox(composeBox) {
    if (isLoading) {
        composeBox.innerHTML = '<p><em>Generating AI message...</em></p>';
        return;
    }
    if (isError) {
        composeBox.innerHTML = `<p><strong>Error:</strong><br>${generatedMessage}</p>`;
        return;
    }
    if (generatedMessage) {
        // LinkedIn's message box is a contenteditable div. We set its innerHTML.
        // We replace newlines with <p> tags for proper formatting.
        const formattedMessage = generatedMessage.split('\n').map(line => `<p>${line}</p>`).join('');
        composeBox.innerHTML = formattedMessage;
        console.log('AI Helper: Message populated.');

        // Dispatch an input event to make LinkedIn's framework recognize the change
        composeBox.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
    }
}

/**
 * Sets up a MutationObserver to watch for the message modal to be added to the DOM.
 */
function observeForMessageBox() {
    const observer = new MutationObserver((mutationsList, obs) => {
        for (const mutation of mutationsList) {
            if (mutation.type === 'childList') {
                // Selector for the message compose box. This may need updating if LinkedIn changes its UI.
                const composeBox = document.querySelector('.msg-form__contenteditable[role="textbox"]');
                if (composeBox) {
                    console.log('AI Helper: Message box detected.');
                    populateMessageBox(composeBox);
                    obs.disconnect(); // Stop observing once we've found and populated the box
                    // We re-initialize the observer in case the user closes and re-opens the message box
                    setTimeout(observeForMessageBox, 1000);
                    return;
                }
            }
        }
    });

    observer.observe(document.body, { childList: true, subtree: true });
    console.log('AI Helper: Observer is now watching for the message box.');
}

// --- Main Execution ---
// We wait a couple of seconds for the page to finish loading its dynamic content.
window.addEventListener('load', () => {
    setTimeout(() => {
        triggerMessageGeneration();
        observeForMessageBox();
    }, 2000); // 2-second delay to ensure SPA framework has loaded content.
});
