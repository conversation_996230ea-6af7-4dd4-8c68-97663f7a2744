<!DOCTYPE html>
<html>
<head>
    <title>AI Helper Options</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-2xl w-full bg-white p-8 rounded-xl shadow-lg">
        <div class="flex items-center mb-6">
            <!-- Icon replaced with an inline SVG symbol -->
            <svg class="h-10 w-10 mr-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 01-2.555-.337A5.972 5.972 0 015.41 20.97a5.969 5.969 0 01-.474-.065 4.48 4.48 0 00.978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25z" />
            </svg>
            <div>
                <h1 class="text-2xl font-bold text-gray-800">LinkedIn AI Message Helper</h1>
                <p class="text-gray-500">Settings</p>
            </div>
        </div>

        <div id="status" class="mb-4 text-center text-green-600 font-medium opacity-0 transition-opacity duration-300">
            Settings saved!
        </div>

        <div class="space-y-6">
            <div>
                <label for="systemPrompt" class="block text-sm font-medium text-gray-700 mb-2">
                    Your System Prompt
                </label>
                <p class="text-xs text-gray-500 mb-2">
                    This is the core instruction for the AI. Tell it how to behave and what kind of message to write.
                    For example: "You are a friendly but professional recruiter. Write a short, personalized outreach message (under 80 words) based on the company's mission. Start with a hook related to their 'About' section."
                </p>
                <textarea id="systemPrompt" rows="6" class="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"></textarea>
            </div>

            <div>
                <button id="save" class="w-full bg-blue-600 text-white font-bold py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 transition-all duration-300 transform hover:scale-105">
                    Save Settings
                </button>
            </div>
        </div>
        <p class="text-center text-xs text-gray-400 mt-8">
            Remember to reload any open LinkedIn company pages after saving for changes to take effect.
        </p>
    </div>

    <script>
        // Function to check if the script is running in the context of a Chrome extension
        const isExtensionContext = () => {
            return typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync;
        };

        // Saves options to chrome.storage
        const saveOptions = () => {
            const status = document.getElementById('status');
            if (!isExtensionContext()) {
                status.textContent = 'Error: Settings can only be saved within the extension.';
                status.className = 'mb-4 text-center text-red-600 font-medium opacity-100 transition-opacity duration-300';
                return;
            }

            const systemPrompt = document.getElementById('systemPrompt').value;

            chrome.storage.sync.set(
                { systemPrompt: systemPrompt },
                () => {
                    // Update status to let user know options were saved.
                    status.textContent = 'Settings saved!';
                    status.className = 'mb-4 text-center text-green-600 font-medium opacity-100 transition-opacity duration-300';
                    setTimeout(() => {
                        status.style.opacity = '0';
                    }, 2000);
                }
            );
        };

        // Restores textarea content using the preferences stored in chrome.storage.
        const restoreOptions = () => {
            const defaultPrompt = "You are a helpful assistant. Based on the provided company overview, write a concise and professional outreach message. Start with a personalized hook that references their mission or values. Keep the message under 100 words.";
            
            if (isExtensionContext()) {
                chrome.storage.sync.get(
                    { systemPrompt: defaultPrompt },
                    (items) => {
                        document.getElementById('systemPrompt').value = items.systemPrompt;
                    }
                );
            } else {
                // If not in an extension context, load the default and show a warning.
                console.warn("Not in extension context. Loading default prompt. Settings will not be saved.");
                document.getElementById('systemPrompt').value = defaultPrompt;
                const status = document.getElementById('status');
                status.textContent = 'Cannot load/save: View this page from extension options.';
                status.className = 'mb-4 text-center text-yellow-600 font-medium opacity-100 transition-opacity duration-300';
            }
        };

        document.addEventListener('DOMContentLoaded', restoreOptions);
        document.getElementById('save').addEventListener('click', saveOptions);
    </script>
</body>
</html>
