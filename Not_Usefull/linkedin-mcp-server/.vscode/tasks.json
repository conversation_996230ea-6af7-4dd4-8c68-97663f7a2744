{"version": "2.0.0", "tasks": [{"label": "bunx @modelcontextprotocol/inspector", "detail": "Run the Model Context Protocol Inspector", "type": "shell", "command": "bunx", "args": ["@modelcontextprotocol/inspector"], "group": {"kind": "test", "isDefault": true}, "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "uv run pre-commit run --all-files", "detail": "Run pre-commit hooks on all files", "type": "shell", "command": "uv", "args": ["run", "pre-commit", "run", "--all-files"], "group": {"kind": "test", "isDefault": false}, "presentation": {"reveal": "never", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "uv run main.py --debug --no-headless --no-lazy-init", "detail": "Run main.py in debug mode with visible window and login immediately", "type": "shell", "command": "uv", "args": ["run", "main.py", "--debug", "--no-headless", "--no-lazy-init"], "group": {"kind": "build", "isDefault": false}, "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "uv run main.py --no-headless --no-lazy-init", "detail": "Run main.py with visible window and login immediately", "type": "shell", "command": "uv", "args": ["run", "main.py", "--no-headless", "--no-lazy-init"], "group": {"kind": "build"}, "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "uv run main.py --no-headless --no-lazy-init --transport streamable-http", "detail": "Start HTTP MCP server on localhost:8000/mcp", "type": "shell", "command": "uv", "args": ["run", "main.py", "--no-headless", "--no-lazy-init", "--transport", "streamable-http"], "isBackground": true, "group": {"kind": "build", "isDefault": false}, "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "uv run tail -n 20 -F ~/Library/Logs/Claude/mcp*.log", "detail": "Follow <PERSON>", "type": "shell", "command": "tail", "args": ["-n", "20", "-F", "~/Library/Logs/Claude/mcp*.log"], "isBackground": true, "presentation": {"reveal": "always", "panel": "new", "focus": false}, "problemMatcher": []}, {"label": "bunx @anthropic-ai/dxt pack", "detail": "Pack the DXT package", "type": "shell", "command": "bunx", "args": ["@anthropic-ai/dxt", "pack"], "group": {"kind": "build", "isDefault": false}, "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}]}