{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}, "editor.defaultFormatter": "charliermarsh.ruff", "[python]": {"editor.defaultFormatter": "charliermarsh.ruff", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports.ruff": "explicit"}}, "python.defaultInterpreterPath": ".venv/bin/python", "python.terminal.activateEnvironment": true, "yaml.schemas": {"https://www.schemastore.org/github-issue-config.json": "file:///Users/<USER>/Documents/development/python/linkedin-mcp-server/.github/ISSUE_TEMPLATE/config.yml"}, "cursorpyright.analysis.autoImportCompletions": true, "cursorpyright.analysis.diagnosticMode": "workspace", "cursorpyright.analysis.extraPaths": ["./linkedin_mcp_server"], "cursorpyright.analysis.stubPath": "./linkedin_mcp_server", "cursorpyright.analysis.typeCheckingMode": "off"}