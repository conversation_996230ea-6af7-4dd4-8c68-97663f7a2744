# .pre-commit-config.yaml
repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.1.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files
    -   id: check-merge-conflict
    -   id: debug-statements

-   repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.9.10
    hooks:
    -   id: ruff
        args: [--fix]
    -   id: ruff-format

-   repo: local
    hooks:
    -   id: ty
        name: ty
        entry: uv run ty check
        language: system
        types: [python]
        pass_filenames: false
