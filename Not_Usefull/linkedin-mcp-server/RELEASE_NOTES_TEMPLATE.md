For an installation guide, refer to the [README](https://github.com/stickerdaniel/linkedin-mcp-server/blob/main/README.md).

## 🐳 Update Docker Installation
**For users with Docker-based MCP client configurations:**
```bash
docker pull stickerdaniel/linkedin-mcp-server:latest
```
The `latest` tag will always point to the most recent release.
To pull this specific version, run:
```bash
docker pull stickerdaniel/linkedin-mcp-server:${VERSION}
```

## 📦 Update DXT Extension Installation
**For Claude Desktop users:**
1. Download the `.dxt` file below
2. Double-click to install in Claude Desktop
3. Restart Claude Desktop

This DXT extension uses the pinned version `${VERSION}`, the Docker image will be pulled automatically.
