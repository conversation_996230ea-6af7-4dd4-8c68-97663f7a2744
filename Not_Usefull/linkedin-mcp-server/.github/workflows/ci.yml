# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  lint-and-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up uv
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true

      - name: Install dependencies
        run: |
          uv sync
          uv sync --group dev

      - name: Run pre-commit hooks
        uses: pre-commit/action@v3.0.1

      - name: Optimize uv cache for CI
        run: uv cache prune --ci
