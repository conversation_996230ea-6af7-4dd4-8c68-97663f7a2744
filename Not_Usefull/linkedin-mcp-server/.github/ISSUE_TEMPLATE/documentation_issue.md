---
name: Documentation Issue
about: Report problems with README, setup instructions, or other documentation
title: '[DOCS] '
labels: ['documentation']
assignees: ''

---

## Documentation Problem
**What documentation issue did you find?**
- [ ] Incorrect/outdated setup instructions
- [ ] Missing information
- [ ] Unclear/confusing explanations
- [ ] Broken links
- [ ] Example code doesn't work
- [ ] Missing prerequisites
- [ ] Inconsistent information
- [ ] Typos/grammar issues
- [ ] Other: ___________

## Location
**Where is the documentation issue?**
- [ ] README.md
- [ ] Code comments
- [ ] Error messages
- [ ] CLI help text
- [ ] Other: ___________

**Specific section/line:**
___________

## Current Documentation
**What does the documentation currently say?**
```
Paste the current text or link to the specific section
```

## Problem Description
**What's wrong or confusing about it?**
A clear description of why this documentation is problematic.

## Suggested Fix
**What should it say instead?**
```
Suggested replacement text or improvements
```

## Additional Context
Add any other context, screenshots, or examples that would help improve the documentation.
