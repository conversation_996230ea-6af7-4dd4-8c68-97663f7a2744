{"cells": [{"cell_type": "code", "execution_count": 1, "id": "22fb28e0", "metadata": {}, "outputs": [], "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "import pandas as pd\n", "import re\n", "import html"]}, {"cell_type": "markdown", "id": "1d0fa418", "metadata": {}, "source": ["# Load Company Data"]}, {"cell_type": "code", "execution_count": 2, "id": "62648c1b", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv('company.csv', header=None)\n", "df.columns = ['name', 'url']"]}, {"cell_type": "code", "execution_count": 3, "id": "1027cef4", "metadata": {}, "outputs": [], "source": ["def linked_profiles_fetcher(company_url):\n", "    try:\n", "        response = requests.get(company_url, timeout=10)\n", "        response.raise_for_status()\n", "    except requests.RequestException as e:\n", "        print(f\"Request failed: {e}\")\n", "        return []\n", "\n", "    soup = BeautifulSoup(response.text, \"html.parser\")\n", "    profiles = set()\n", "    comp_profile = set()\n", "\n", "    # Pattern to match any LinkedIn profile URL\n", "    linkedin_pattern = re.compile(\n", "        r\"https?://(?:www\\.)?linkedin\\.com/in/[a-zA-Z0-9\\-~_%]+/?\", re.IGNORECASE\n", "    )\n", "    linked_comp_patter = re.compile(\n", "        r\"https?://(?:www\\.)?linkedin\\.com/company/[a-zA-Z0-9\\-~_%]+/?\", re.IGNORECASE\n", "    )\n", "\n", "    # Search all <a href> attributes\n", "    for a in soup.find_all(\"a\", href=True):\n", "        href = html.unescape(a['href'])  # decode HTML entities like &quot;\n", "        \n", "        matches = linkedin_pattern.findall(href)\n", "        profiles.update(matches)\n", "        \n", "        matches_comp = linked_comp_patter.findall(href)\n", "        comp_profile.update(matches_comp)\n", "\n", "    # Search the entire page text for embedded LinkedIn URLs\n", "    full_text = html.unescape(soup.get_text() + response.text)\n", "    profiles.update(linkedin_pattern.findall(full_text))\n", "    comp_profile.update(linked_comp_patter.findall(full_text))\n", "\n", "    return profiles, comp_profile\n", "\n", "\n", "from urllib.parse import urlparse, urlunparse\n", "\n", "def normalize_linkedin_url(url):\n", "    if not isinstance(url, str):\n", "        return None\n", "\n", "    try:\n", "        parsed = urlparse(url)\n", "        if 'linkedin.com/in/' not in parsed.netloc + parsed.path:\n", "            return None  # Not a valid LinkedIn profile path\n", "\n", "        scheme = 'https'\n", "        netloc = 'www.linkedin.com'\n", "        path = parsed.path.rstrip('/')  # remove trailing slash\n", "        return urlunparse((scheme, netloc, path, '', '', ''))\n", "    except Exception:\n", "        return None\n", "    \n", "def normalize_linkedin_url_comp(url):\n", "    if not isinstance(url, str):\n", "        return None\n", "\n", "    try:\n", "        parsed = urlparse(url)\n", "        if 'linkedin.com/company/' not in parsed.netloc + parsed.path:\n", "            return None  # Not a valid LinkedIn profile path\n", "\n", "        scheme = 'https'\n", "        netloc = 'www.linkedin.com'\n", "        path = parsed.path.rstrip('/')  # remove trailing slash\n", "        return urlunparse((scheme, netloc, path, '', '', ''))\n", "    except Exception:\n", "        return None"]}, {"cell_type": "code", "execution_count": 4, "id": "07fc1c59", "metadata": {}, "outputs": [], "source": ["db = {}\n", "for i in range(len(df)):\n", "    profiles, linked_page = linked_profiles_fetcher(df.iloc[i]['url'])\n", "    clean_profiles = set(filter(None, (normalize_linkedin_url(p) for p in profiles)))\n", "    clean_linked_page = set(filter(None, (normalize_linkedin_url_comp(p) for p in linked_page)))\n", "    \n", "    db[df.iloc[i]['name']] = {\"founders\" : clean_profiles, \"YC_url\" : df.iloc[i]['url'], \"company_linked_page\": clean_linked_page}"]}, {"cell_type": "code", "execution_count": 5, "id": "3f9544de", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "def convert_sets_to_lists(obj):\n", "    if isinstance(obj, dict):\n", "        return {k: convert_sets_to_lists(v) for k, v in obj.items()}\n", "    elif isinstance(obj, set):\n", "        return list(obj)\n", "    elif isinstance(obj, list):\n", "        return [convert_sets_to_lists(i) for i in obj]\n", "    else:\n", "        return obj\n", "    \n", "    \n", "clean_data = convert_sets_to_lists(db)\n", "\n", "with open('company_data.json', 'w') as f:\n", "    json.dump(clean_data, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "id": "ea811df0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "c2ebf0a3", "metadata": {}, "source": ["# Start with getting in"]}, {"cell_type": "code", "execution_count": 2, "id": "89251e8a", "metadata": {}, "outputs": [], "source": ["url = \"https://www.linkedin.com/company/capacitive\"\n", "\n", "response = requests.get(url, timeout=10)\n", "response.raise_for_status()\n", "soup = BeautifulSoup(response.text, \"html.parser\")"]}, {"cell_type": "code", "execution_count": null, "id": "70cc5131", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "1d3f9c50", "metadata": {}, "source": ["# MCPP"]}, {"cell_type": "code", "execution_count": null, "id": "5f37a06b", "metadata": {}, "outputs": [], "source": ["# 1. Clone repository\n", "\n", "cd linkedin-mcp-server\n", "\n", "# 2. Install UV package manager\n", "curl -LsSf https://astral.sh/uv/install.sh | sh\n", "uv python # install python if you don't have it\n", "\n", "# 3. Install dependencies and dev dependencies\n", "uv sync\n", "uv sync --group dev\n", "\n", "# 4. Install pre-commit hooks\n", "uv run pre-commit install\n", "uv run main.py --no-headless --no-lazy-init"]}, {"cell_type": "code", "execution_count": null, "id": "59ec0923", "metadata": {}, "outputs": [], "source": ["# Install Chrome dependencies and keyring fallback backend\n", "!apt-get update\n", "!apt-get install -y libnss3 libatk-bridge2.0-0 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 libxi6 libxtst6 libxrandr2 libgbm1 libpangocairo-1.0-0 libatk1.0-0 libcups2 libdrm2 libxss1\n", "!pip install keyrings.alt"]}, {"cell_type": "code", "execution_count": null, "id": "40bbb95a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b0274b74", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}