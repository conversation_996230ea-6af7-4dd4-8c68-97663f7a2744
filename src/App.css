.app {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background: #0a0a0a;
  color: #ffffff;
}

/* Three.js Background */
.three-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* Main Content */
.main-content {
  position: relative;
  z-index: 2;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 0 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.header {
  text-align: center;
  padding: 2rem 0 1rem;
  background: rgba(10, 10, 10, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 0 0 20px 20px;
  margin-bottom: 1rem;
}

.header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ff6b35 0%, #4a90e2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tagline {
  font-size: 1rem;
  color: #b0b0b0;
  margin: 0;
}

/* Content Grid */
.content-grid {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: center;
  padding: 0.5rem 0;
}

/* Left Section */
.left-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.hero-title {
  font-size: 2.8rem;
  font-weight: 800;
  line-height: 1.2;
  margin: 0;
  background: linear-gradient(135deg, #ffffff 0%, #ff6b35 50%, #4a90e2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.1rem;
  color: #b0b0b0;
  line-height: 1.6;
  margin: 0;
}

.cta-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.cta-button {
  background: linear-gradient(135deg, #ff6b35 0%, #4a90e2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
  align-self: flex-start;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(255, 107, 53, 0.5);
}

.cta-subtitle {
  color: #888;
  font-size: 0.9rem;
  margin: 0;
}

/* Right Section */
.right-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 107, 53, 0.2);
  border-radius: 15px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 107, 53, 0.5);
  box-shadow: 0 10px 30px rgba(255, 107, 53, 0.2);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.feature-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #ff6b35;
}

.feature-description {
  font-size: 0.85rem;
  color: #b0b0b0;
  line-height: 1.4;
  margin: 0;
}

/* How It Works - Bottom Section */
.how-it-works {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(74, 144, 226, 0.2);
  border-radius: 20px;
  padding: 1.2rem;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
}

.how-it-works h3 {
  text-align: center;
  font-size: 1.2rem;
  margin-bottom: 0.8rem;
  color: #4a90e2;
}

.steps {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.8rem;
}

.step {
  text-align: center;
  padding: 0.8rem;
}

.step-number {
  width: 35px;
  height: 35px;
  background: linear-gradient(135deg, #ff6b35 0%, #4a90e2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: bold;
  margin: 0 auto 0.4rem;
}

.step-title {
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 0.2rem;
  color: #ffffff;
}

.step-description {
  font-size: 0.75rem;
  color: #888;
  margin: 0;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 107, 53, 0.3);
  border-radius: 20px;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
  position: relative;
  box-shadow: 0 20px 40px rgba(255, 107, 53, 0.2);
  max-height: 90vh;
  overflow-y: auto;
}

.modal-close {
  position: absolute;
  top: 15px;
  right: 20px;
  background: none;
  border: none;
  font-size: 28px;
  cursor: pointer;
  color: #888;
  transition: color 0.3s ease;
}

.modal-close:hover {
  color: #ff6b35;
}

.modal-content h2 {
  margin-bottom: 10px;
  color: #ffffff;
  background: linear-gradient(135deg, #ff6b35 0%, #4a90e2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-content>p {
  margin-bottom: 30px;
  color: #b0b0b0;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #ffffff;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 107, 53, 0.3);
  border-radius: 10px;
  font-size: 1rem;
  color: #ffffff;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #ff6b35;
  box-shadow: 0 0 15px rgba(255, 107, 53, 0.3);
  background: rgba(255, 255, 255, 0.15);
}

.form-group input::placeholder {
  color: #888;
}

/* Payment Type Toggle */
.payment-type-toggle {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.toggle-btn {
  flex: 1;
  padding: 0.8rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 107, 53, 0.3);
  border-radius: 8px;
  color: #b0b0b0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.toggle-btn.active {
  background: linear-gradient(135deg, #ff6b35 0%, #4a90e2 100%);
  border-color: #ff6b35;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
}

.toggle-btn:hover:not(.active) {
  border-color: #ff6b35;
  background: rgba(255, 107, 53, 0.1);
}

/* Price Slider */
.price-slider-container {
  margin-top: 1rem;
}

.price-display {
  text-align: center;
  font-size: 1.2rem;
  font-weight: 600;
  color: #ff6b35;
  margin-bottom: 1rem;
  padding: 0.8rem;
  background: rgba(255, 107, 53, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(255, 107, 53, 0.3);
}

.price-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  margin-bottom: 0.5rem;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.price-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b35 0%, #4a90e2 100%);
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(255, 107, 53, 0.4);
  transition: all 0.3s ease;
}

.price-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.6);
}

.price-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b35 0%, #4a90e2 100%);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 10px rgba(255, 107, 53, 0.4);
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #888;
  margin-top: 0.5rem;
}

.submit-btn {
  width: 100%;
  background: linear-gradient(135deg, #ff6b35 0%, #4a90e2 100%);
  color: white;
  border: none;
  padding: 15px;
  font-size: 1.1rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.success-message {
  text-align: center;
  padding: 30px;
}

.success-message h3 {
  color: #4a90e2;
  margin-bottom: 15px;
}

.success-message p {
  color: #b0b0b0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .right-section {
    grid-template-columns: repeat(2, 1fr);
  }

  .hero-title {
    font-size: 2.2rem;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 0 1rem;
  }

  .header {
    padding: 1.5rem 0 1rem;
  }

  .header h1 {
    font-size: 2rem;
  }

  .hero-title {
    font-size: 1.8rem;
  }

  .right-section {
    grid-template-columns: 1fr;
  }

  .steps {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }

  .how-it-works {
    padding: 1rem;
    margin-bottom: 0.5rem;
  }

  .modal-content {
    padding: 1.5rem;
    margin: 1rem;
  }
}

@media (max-width: 480px) {
  .header h1 {
    font-size: 1.5rem;
  }

  .hero-title {
    font-size: 1.5rem;
  }

  .feature-card {
    padding: 1rem;
  }

  .cta-button {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }
}