* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-height: 100vh;
  background: #0a0a0a;
  overflow: hidden;
}

#root {
  min-height: 100vh;
}

a {
  color: #ff6b35;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}