const Hero = ({ onInterestedClick }) => {
  const features = [
    {
      icon: "🤖",
      title: "AI-Powered Messages",
      description: "Generate professional, personalized outreach messages using company information"
    },
    {
      icon: "⚡",
      title: "Auto-Population",
      description: "Automatically fills message compose boxes with generated content"
    },
    {
      icon: "🎯",
      title: "Company Integration",
      description: "Extracts company details directly from LinkedIn pages"
    },
    {
      icon: "📝",
      title: "Professional Templates",
      description: "Uses well-crafted message templates that can be customized"
    }
  ]

  return (
    <section className="hero">
      <div className="container">
        <div className="hero-content">
          <h2>Transform Your LinkedIn Outreach</h2>
          <p>A Chrome extension that automatically generates personalized messages for LinkedIn company pages using AI.</p>
          
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature">
                <div className="feature-icon">{feature.icon}</div>
                <h3>{feature.title}</h3>
                <p>{feature.description}</p>
              </div>
            ))}
          </div>

          <div className="cta-section">
            <button onClick={onInterestedClick} className="cta-button">
              I'm Interested!
            </button>
            <p className="cta-subtitle">Join the waitlist and be the first to know when it's available</p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
