const HowItWorks = () => {
  const steps = [
    {
      number: 1,
      title: "Install Extension",
      description: "Add the Chrome extension to your browser"
    },
    {
      number: 2,
      title: "Visit Company Page",
      description: "Navigate to any LinkedIn company page"
    },
    {
      number: 3,
      title: "Click Message",
      description: "Click the \"Message\" button and watch the magic happen"
    }
  ]

  return (
    <section className="how-it-works">
      <div className="container">
        <h2>How It Works</h2>
        <div className="steps">
          {steps.map((step, index) => (
            <div key={index} className="step">
              <div className="step-number">{step.number}</div>
              <h3>{step.title}</h3>
              <p>{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default HowItWorks
