import { useState, useEffect } from 'react'
import emailjs from '@emailjs/browser'

const InterestModal = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    price: 5,
    paymentType: 'monthly'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  const [emailError, setEmailError] = useState('')

  // Initialize EmailJS
  useEffect(() => {
    emailjs.init("RjT2uUrmRT_-5uTs-")
  }, [])

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear email error when user starts typing
    if (name === 'email' && emailError) {
      setEmailError('')
    }
  }

  // Handle slider change
  const handleSliderChange = (e) => {
    setFormData(prev => ({
      ...prev,
      price: parseInt(e.target.value)
    }))
  }

  // Handle payment type change
  const handlePaymentTypeChange = (type) => {
    setFormData(prev => ({
      ...prev,
      paymentType: type
    }))
  }

  // Get formatted price string
  const getFormattedPrice = () => {
    if (formData.paymentType === 'monthly') {
      return `$${formData.price}/month`
    } else {
      const oneTimePrice = formData.price * 12
      return `$${oneTimePrice} one-time payment`
    }
  }

  // Validate email format
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // Handle email blur for validation
  const handleEmailBlur = () => {
    if (formData.email && !validateEmail(formData.email)) {
      setEmailError('Please enter a valid email address')
    } else {
      setEmailError('')
    }
  }

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()

    // Validate form
    if (!formData.name || !formData.email) {
      alert('Please fill in all required fields.')
      return
    }

    if (!validateEmail(formData.email)) {
      setEmailError('Please enter a valid email address')
      return
    }

    setIsSubmitting(true)

    try {
      // Send email using EmailJS
      await emailjs.send(
        'service_5wawluq', // Your service ID
        'template_69uc696', // Your template ID
        {
          name: formData.name,
          mail: formData.email, // Using 'mail' as per your template
          price: getFormattedPrice()
        }
      )

      console.log('Email sent successfully!')
      setShowSuccess(true)

      // Auto-close modal after 3 seconds
      setTimeout(() => {
        handleClose()
      }, 3000)

    } catch (error) {
      console.error('Failed to send email:', error)
      alert('Sorry, there was an error sending your information. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle modal close
  const handleClose = () => {
    setFormData({ name: '', email: '', price: 5, paymentType: 'monthly' })
    setShowSuccess(false)
    setEmailError('')
    setIsSubmitting(false)
    onClose()
  }

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen])

  // Handle background click
  const handleBackgroundClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose()
    }
  }

  if (!isOpen) return null

  return (
    <div className="modal-overlay" onClick={handleBackgroundClick}>
      <div className="modal-content">
        <button className="modal-close" onClick={handleClose}>
          &times;
        </button>

        {!showSuccess ? (
          <>
            <h2>Join Our Waitlist</h2>
            <p>Get early access to LinkedIn AI Message Helper</p>

            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="name">Full Name *</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter your full name"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="email">Email Address *</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  onBlur={handleEmailBlur}
                  placeholder="Enter your email address"
                  style={{
                    borderColor: emailError ? '#e74c3c' : 'rgba(255, 107, 53, 0.3)',
                    boxShadow: emailError ? '0 0 5px rgba(231, 76, 60, 0.3)' : 'none'
                  }}
                  required
                />
                {emailError && (
                  <span style={{ color: '#e74c3c', fontSize: '0.9rem', marginTop: '5px', display: 'block' }}>
                    {emailError}
                  </span>
                )}
              </div>

              <div className="form-group">
                <label>How much would you be willing to pay? *</label>

                {/* Payment Type Toggle */}
                <div className="payment-type-toggle">
                  <button
                    type="button"
                    className={`toggle-btn ${formData.paymentType === 'monthly' ? 'active' : ''}`}
                    onClick={() => handlePaymentTypeChange('monthly')}
                  >
                    Monthly
                  </button>
                  <button
                    type="button"
                    className={`toggle-btn ${formData.paymentType === 'onetime' ? 'active' : ''}`}
                    onClick={() => handlePaymentTypeChange('onetime')}
                  >
                    One-time
                  </button>
                </div>

                {/* Price Slider */}
                <div className="price-slider-container">
                  <div className="price-display">
                    {getFormattedPrice()}
                  </div>
                  <input
                    type="range"
                    min="3"
                    max="10"
                    value={formData.price}
                    onChange={handleSliderChange}
                    className="price-slider"
                  />
                  <div className="slider-labels">
                    <span>$3</span>
                    <span>$10</span>
                  </div>
                </div>
              </div>

              <button
                type="submit"
                className="submit-btn"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Sending...' : 'Submit'}
              </button>
            </form>
          </>
        ) : (
          <div className="success-message">
            <h3>Thank you!</h3>
            <p>We've received your information and will be in touch soon.</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default InterestModal
