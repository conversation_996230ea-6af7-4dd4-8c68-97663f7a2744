const MainContent = ({ onInterestedClick }) => {
  const features = [
    {
      icon: "🤖",
      title: "AI-Powered",
      description: "Generate professional messages using company information"
    },
    {
      icon: "⚡",
      title: "Auto-Fill",
      description: "Automatically populates message compose boxes"
    },
    {
      icon: "🎯",
      title: "Smart Extract",
      description: "Extracts company details from LinkedIn pages"
    },
    {
      icon: "📝",
      title: "Pro Templates",
      description: "Uses well-crafted customizable templates"
    }
  ]

  const steps = [
    {
      number: 1,
      title: "Install",
      description: "Add Chrome extension"
    },
    {
      number: 2,
      title: "Visit",
      description: "Go to LinkedIn company page"
    },
    {
      number: 3,
      title: "Message",
      description: "Click and watch magic happen"
    }
  ]

  return (
    <div className="main-content">
      {/* Header */}
      <header className="header">
        <h1>LinkedIn AI Message Helper</h1>
        <p className="tagline">Automate your LinkedIn outreach with AI-powered messages</p>
      </header>

      {/* Main Content Grid */}
      <div className="content-grid">
        {/* Left Section - Hero */}
        <div className="left-section">
          <h2 className="hero-title">Transform Your LinkedIn Outreach</h2>
          <p className="hero-description">
            A Chrome extension that automatically generates personalized messages for LinkedIn company pages using AI. 
            Save time and increase your response rates with intelligent automation.
          </p>
          
          <div className="cta-section">
            <button onClick={onInterestedClick} className="cta-button">
              I'm Interested!
            </button>
            <p className="cta-subtitle">Join the waitlist and be the first to know when it's available</p>
          </div>
        </div>

        {/* Right Section - Features */}
        <div className="right-section">
          {features.map((feature, index) => (
            <div key={index} className="feature-card">
              <span className="feature-icon">{feature.icon}</span>
              <h3 className="feature-title">{feature.title}</h3>
              <p className="feature-description">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* How It Works - Bottom Section */}
      <div className="how-it-works">
        <h3>How It Works</h3>
        <div className="steps">
          {steps.map((step, index) => (
            <div key={index} className="step">
              <div className="step-number">{step.number}</div>
              <h4 className="step-title">{step.title}</h4>
              <p className="step-description">{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default MainContent
