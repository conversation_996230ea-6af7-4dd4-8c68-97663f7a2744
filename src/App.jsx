import { useState } from 'react'
import ThreeBackground from './components/ThreeBackground'
import MainContent from './components/MainContent'
import InterestModal from './components/InterestModal'
import './App.css'

function App() {
  const [isModalOpen, setIsModalOpen] = useState(false)

  const openModal = () => setIsModalOpen(true)
  const closeModal = () => setIsModalOpen(false)

  return (
    <div className="app">
      <ThreeBackground />
      <MainContent onInterestedClick={openModal} />
      <InterestModal isOpen={isModalOpen} onClose={closeModal} />
    </div>
  )
}

export default App
