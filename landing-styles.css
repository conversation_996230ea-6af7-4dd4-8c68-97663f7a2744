* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    text-align: center;
    padding: 40px 0;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.tagline {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Hero Section */
.hero {
    background: white;
    border-radius: 20px;
    padding: 60px 40px;
    margin-bottom: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.hero-content h2 {
    font-size: 2.2rem;
    text-align: center;
    margin-bottom: 20px;
    color: #2c3e50;
}

.hero-content > p {
    font-size: 1.2rem;
    text-align: center;
    margin-bottom: 50px;
    color: #666;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.feature {
    text-align: center;
    padding: 30px 20px;
    border-radius: 15px;
    background: #f8f9fa;
    transition: transform 0.3s ease;
}

.feature:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.feature h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #2c3e50;
}

.feature p {
    color: #666;
    line-height: 1.5;
}

/* CTA Section */
.cta-section {
    text-align: center;
}

.cta-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 18px 40px;
    font-size: 1.2rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
}

.cta-subtitle {
    margin-top: 15px;
    color: #666;
    font-style: italic;
}

/* How It Works */
.how-it-works {
    background: white;
    border-radius: 20px;
    padding: 60px 40px;
    margin-bottom: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.how-it-works h2 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 50px;
    color: #2c3e50;
}

.steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.step {
    text-align: center;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 20px;
}

.step h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #2c3e50;
}

.step p {
    color: #666;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 40px;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    position: relative;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 15px;
}

.close:hover {
    color: #000;
}

.modal-content h2 {
    margin-bottom: 10px;
    color: #2c3e50;
}

.modal-content > p {
    margin-bottom: 30px;
    color: #666;
}

/* Form Styles */
.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.submit-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px;
    font-size: 1.1rem;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.success-message {
    text-align: center;
    padding: 30px;
}

.success-message h3 {
    color: #27ae60;
    margin-bottom: 15px;
}

.success-message p {
    color: #666;
}

/* Responsive */
@media (max-width: 768px) {
    .header h1 {
        font-size: 2rem;
    }
    
    .hero {
        padding: 40px 20px;
    }
    
    .hero-content h2 {
        font-size: 1.8rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .steps {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .modal-content {
        margin: 10% auto;
        padding: 30px 20px;
    }
}
