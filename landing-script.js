// Initialize EmailJS
(function() {
    emailjs.init("RjT2uUrmRT_-5uTs-"); // Your public key
})();

// DOM elements
const interestedBtn = document.getElementById('interestedBtn');
const modal = document.getElementById('interestModal');
const closeBtn = document.querySelector('.close');
const form = document.getElementById('interestForm');
const successMessage = document.getElementById('successMessage');

// Open modal when "I'm Interested" button is clicked
interestedBtn.addEventListener('click', function() {
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
});

// Close modal when X is clicked
closeBtn.addEventListener('click', function() {
    closeModal();
});

// Close modal when clicking outside of it
window.addEventListener('click', function(event) {
    if (event.target === modal) {
        closeModal();
    }
});

// Close modal function
function closeModal() {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto'; // Restore scrolling
    
    // Reset form if it was submitted
    if (successMessage.style.display === 'block') {
        form.style.display = 'block';
        successMessage.style.display = 'none';
        form.reset();
    }
}

// Handle form submission
form.addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Get form data
    const formData = {
        name: document.getElementById('name').value,
        mail: document.getElementById('email').value, // Note: using 'mail' as per your template
        price: document.getElementById('price').value
    };
    
    // Validate form
    if (!formData.name || !formData.mail || !formData.price) {
        alert('Please fill in all required fields.');
        return;
    }
    
    // Disable submit button and show loading state
    const submitBtn = form.querySelector('.submit-btn');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Sending...';
    submitBtn.disabled = true;
    
    // Send email using EmailJS
    emailjs.send(
        'service_5wawluq', // Your service ID
        'template_69uc696', // Your template ID
        {
            name: formData.name,
            mail: formData.mail,
            price: formData.price
        }
    )
    .then(function(response) {
        console.log('SUCCESS!', response.status, response.text);
        
        // Hide form and show success message
        form.style.display = 'none';
        successMessage.style.display = 'block';
        
        // Auto-close modal after 3 seconds
        setTimeout(function() {
            closeModal();
        }, 3000);
        
    }, function(error) {
        console.log('FAILED...', error);
        alert('Sorry, there was an error sending your information. Please try again.');
        
        // Re-enable submit button
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
});

// Add some smooth scrolling and animations
document.addEventListener('DOMContentLoaded', function() {
    // Animate features on scroll
    const features = document.querySelectorAll('.feature');
    const steps = document.querySelectorAll('.step');
    
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Initially hide elements and observe them
    [...features, ...steps].forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(element);
    });
    
    // Add pulse animation to CTA button
    setInterval(function() {
        interestedBtn.style.transform = 'scale(1.05)';
        setTimeout(function() {
            interestedBtn.style.transform = 'scale(1)';
        }, 200);
    }, 3000);
});

// Add keyboard support for modal
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && modal.style.display === 'block') {
        closeModal();
    }
});

// Form validation enhancements
document.getElementById('email').addEventListener('blur', function() {
    const email = this.value;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (email && !emailRegex.test(email)) {
        this.style.borderColor = '#e74c3c';
        this.style.boxShadow = '0 0 5px rgba(231, 76, 60, 0.3)';
    } else {
        this.style.borderColor = '#e1e8ed';
        this.style.boxShadow = 'none';
    }
});

// Add focus states for better accessibility
const formInputs = document.querySelectorAll('input, select');
formInputs.forEach(input => {
    input.addEventListener('focus', function() {
        this.style.borderColor = '#667eea';
        this.style.boxShadow = '0 0 10px rgba(102, 126, 234, 0.2)';
    });
    
    input.addEventListener('blur', function() {
        if (this.id !== 'email') { // Email has its own validation
            this.style.borderColor = '#e1e8ed';
            this.style.boxShadow = 'none';
        }
    });
});
